#!/usr/bin/env python3
"""
Debug script để kiểm tra API call và tìm lỗi
"""

import asyncio
import sys
from pathlib import Path
import httpx
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def check_task_status(task_id):
    """Kiểm tra status của task"""
    print(f"\n🔍 Checking task status: {task_id}")
    
    try:
        async with httpx.AsyncClient() as client:
            # Check task status
            status_response = await client.get(f"http://localhost:8000/api/v1/tasks/{task_id}/status")
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"✅ Task status: {status_data.get('status')}")
                print(f"   Progress: {status_data.get('progress', 0)}%")
                print(f"   Message: {status_data.get('message', 'N/A')}")
                
                # If completed, get result
                if status_data.get('status') == 'completed':
                    result_response = await client.get(f"http://localhost:8000/api/v1/tasks/{task_id}/result")
                    
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        print(f"\n📋 Task result:")
                        print(f"   Success: {result_data.get('success')}")
                        print(f"   Book ID: {result_data.get('book_id')}")
                        print(f"   Lesson ID: {result_data.get('lesson_id')}")
                        print(f"   File URL: {result_data.get('file_url', 'N/A')}")
                        print(f"   Embeddings created: {result_data.get('embeddings_created')}")
                        
                        if not result_data.get('file_url'):
                            print("❌ No file_url in result - Supabase upload failed!")
                        else:
                            print("✅ File URL present - Supabase upload successful!")
                            
                        return result_data
                    else:
                        print(f"❌ Failed to get task result: {result_response.status_code}")
                        
                elif status_data.get('status') == 'failed':
                    print(f"❌ Task failed: {status_data.get('error', 'Unknown error')}")
                    
            else:
                print(f"❌ Failed to get task status: {status_response.status_code}")
                
    except Exception as e:
        print(f"❌ Error checking task: {e}")
        
    return None

async def simulate_api_call():
    """Mô phỏng API call để debug"""
    print("🔍 Simulating API Call")
    print("=" * 50)
    
    # Create test DOCX content
    import zipfile
    import io
    
    docx_buffer = io.BytesIO()
    
    with zipfile.ZipFile(docx_buffer, 'w', zipfile.ZIP_DEFLATED) as docx_zip:
        content_types = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>'''
        docx_zip.writestr('[Content_Types].xml', content_types)
        
        rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>'''
        docx_zip.writestr('_rels/.rels', rels)
        
        document = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>Giáo án mẫu - Test DOCX upload</w:t>
            </w:r>
        </w:p>
        <w:p>
            <w:r>
                <w:t>Đây là nội dung test để kiểm tra upload DOCX lên Supabase.</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''
        docx_zip.writestr('word/document.xml', document)
    
    docx_content = docx_buffer.getvalue()
    print(f"✅ Created test DOCX content ({len(docx_content)} bytes)")
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Prepare form data
            files = {
                'file': ('giaoanmau_test.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            data = {
                'book_id': '5',
                'create_embeddings': 'true',
                'lesson_id': '1',
                'isImportGuide': 'true'
            }
            
            print(f"\n📤 Making API call...")
            print(f"   URL: http://localhost:8000/api/v1/pdf/import")
            print(f"   File: giaoanmau_test.docx ({len(docx_content)} bytes)")
            print(f"   Data: {data}")
            
            # Make API call
            response = await client.post(
                "http://localhost:8000/api/v1/pdf/import",
                files=files,
                data=data
            )
            
            print(f"\n📋 API Response:")
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"   Success: {response_data.get('success')}")
                print(f"   Task ID: {response_data.get('task_id')}")
                print(f"   Message: {response_data.get('message')}")
                
                # Wait a bit and check task status
                task_id = response_data.get('task_id')
                if task_id:
                    print(f"\n⏳ Waiting 5 seconds for task to process...")
                    await asyncio.sleep(5)
                    
                    result = await check_task_status(task_id)
                    return result
                    
            else:
                print(f"   Error: {response.text}")
                
    except Exception as e:
        print(f"❌ API call failed: {e}")
        import traceback
        traceback.print_exc()
        
    return None

async def check_guides_api():
    """Kiểm tra API get guides"""
    print("\n🔍 Checking Guides API")
    print("=" * 30)
    
    try:
        async with httpx.AsyncClient() as client:
            # Get all guides
            response = await client.get("http://localhost:8000/api/v1/pdf/guides")
            
            if response.status_code == 200:
                data = response.json()
                guides = data.get('data', {}).get('guides', [])
                print(f"✅ Found {len(guides)} guides total")
                
                # Check for book_id=5
                book_5_guides = [g for g in guides if g.get('book_id') == '5']
                print(f"✅ Found {len(book_5_guides)} guides for book_id=5")
                
                for guide in book_5_guides:
                    print(f"   - Lesson: {guide.get('lesson_id')}")
                    print(f"     File URL: {guide.get('file_url', 'N/A')}")
                    print(f"     Upload time: {guide.get('uploaded_at', 'N/A')}")
                    
                # Check for book_id=5 specifically
                response2 = await client.get("http://localhost:8000/api/v1/pdf/guides?book_id=5")
                
                if response2.status_code == 200:
                    data2 = response2.json()
                    filtered_guides = data2.get('data', {}).get('guides', [])
                    print(f"✅ Filtered API found {len(filtered_guides)} guides for book_id=5")
                    
            else:
                print(f"❌ Guides API failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
    except Exception as e:
        print(f"❌ Error checking guides API: {e}")

async def check_supabase_directly():
    """Kiểm tra Supabase trực tiếp"""
    print("\n🔍 Checking Supabase Directly")
    print("=" * 40)
    
    try:
        from app.services.supabase_storage_service import get_supabase_storage_service
        
        service = get_supabase_storage_service()
        if not service.is_available():
            print("❌ Supabase service not available")
            return
            
        # List files in book_id=5 folder
        result = await service.list_files_by_book_id("5")
        
        if result.get("success"):
            files = result.get("files", [])
            print(f"✅ Found {len(files)} files in book_id=5 folder:")
            
            for file_info in files:
                print(f"   - {file_info.get('name')}")
                print(f"     URL: {file_info.get('file_url')}")
                print(f"     Size: {file_info.get('size', 0)} bytes")
                print(f"     Created: {file_info.get('created_at', 'N/A')}")
        else:
            print(f"❌ Failed to list files: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error checking Supabase: {e}")

async def main():
    """Main debug function"""
    print("🚀 Debugging DOCX Upload Issue")
    print("=" * 50)
    
    # Test API call
    result = await simulate_api_call()
    
    # Check guides API
    await check_guides_api()
    
    # Check Supabase directly
    await check_supabase_directly()
    
    print("\n📋 Debug Summary:")
    print("=" * 30)
    
    if result and result.get('file_url'):
        print("✅ DOCX upload is working correctly")
        print("   The issue might be with your specific file or API call")
    else:
        print("❌ DOCX upload is not working")
        print("   Possible issues:")
        print("   1. Celery worker not processing tasks")
        print("   2. Supabase service not available in worker")
        print("   3. Task processing error")
        print("   4. File type detection issue")
    
    print("\n💡 Next steps:")
    print("   1. Check Celery worker logs")
    print("   2. Ensure Supabase credentials are loaded in worker")
    print("   3. Check task status in MongoDB")
    print("   4. Verify file is valid DOCX format")

if __name__ == "__main__":
    asyncio.run(main())
