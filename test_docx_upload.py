#!/usr/bin/env python3
"""
Test script để kiểm tra việc upload DOCX file lên Supabase trong guide import
"""

import requests
import os
import sys

def test_docx_upload():
    """Test DOCX upload với guide import"""
    
    # API endpoint
    url = "http://localhost:8000/api/v1/pdf/import"
    
    # Tạo một file DOCX test đơn giản (hoặc sử dụng file có sẵn)
    test_file_path = "giaoanmau.docx"
    
    if not os.path.exists(test_file_path):
        print(f"❌ File test không tồn tại: {test_file_path}")
        print("Vui lòng tạo file test hoặc cập nhật đường dẫn")
        return False
    
    # Prepare request data
    files = {
        'file': (test_file_path, open(test_file_path, 'rb'), 
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    }
    
    data = {
        'book_id': '5',
        'create_embeddings': 'true',
        'lesson_id': '1',
        'isImportGuide': 'true'
    }
    
    try:
        print("🚀 Đang gửi request upload DOCX...")
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request thành công!")
            print(f"📋 Task ID: {result.get('task_id')}")
            print(f"📁 Book ID: {result.get('book_id')}")
            print(f"📄 Filename: {result.get('filename')}")
            print(f"🔄 Status: {result.get('status')}")
            
            # Kiểm tra status của task
            task_id = result.get('task_id')
            if task_id:
                check_task_status(task_id)
            
            return True
        else:
            print(f"❌ Request thất bại: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi gửi request: {e}")
        return False
    finally:
        # Đóng file
        files['file'][1].close()

def check_task_status(task_id):
    """Kiểm tra status của task"""
    status_url = f"http://localhost:8000/api/v1/tasks/{task_id}/status"
    
    try:
        print(f"\n🔍 Kiểm tra status của task {task_id}...")
        response = requests.get(status_url)
        
        if response.status_code == 200:
            status = response.json()
            print(f"📊 Task Status: {status.get('status')}")
            print(f"📈 Progress: {status.get('progress', 0)}%")
            print(f"💬 Message: {status.get('message', 'N/A')}")
            
            # Nếu task hoàn thành, lấy kết quả
            if status.get('status') == 'completed':
                get_task_result(task_id)
        else:
            print(f"❌ Không thể lấy status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra status: {e}")

def get_task_result(task_id):
    """Lấy kết quả của task"""
    result_url = f"http://localhost:8000/api/v1/tasks/{task_id}/result"
    
    try:
        print(f"\n📋 Lấy kết quả của task {task_id}...")
        response = requests.get(result_url)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Kết quả task:")
            
            # Kiểm tra thông tin file storage
            file_storage = result.get('file_storage', {})
            if file_storage.get('uploaded_to_supabase'):
                print(f"☁️  File đã upload lên Supabase: {file_storage.get('file_url')}")
                print(f"⏰ Upload time: {file_storage.get('uploaded_at')}")
            else:
                print("❌ File CHƯA được upload lên Supabase")
            
            # Kiểm tra embeddings
            if result.get('embeddings_created'):
                embeddings_info = result.get('embeddings_info', {})
                print(f"🧠 Embeddings đã tạo: {embeddings_info.get('vector_count')} vectors")
            else:
                print("❌ Embeddings chưa được tạo")
                
        else:
            print(f"❌ Không thể lấy kết quả: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Lỗi khi lấy kết quả: {e}")

if __name__ == "__main__":
    print("🧪 Test DOCX Upload to Supabase")
    print("=" * 50)
    
    success = test_docx_upload()
    
    if success:
        print("\n✅ Test hoàn thành!")
    else:
        print("\n❌ Test thất bại!")
        sys.exit(1)
