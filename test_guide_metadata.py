#!/usr/bin/env python3
"""
Test script để kiểm tra metadata của guide trong Qdrant
"""

import requests
import time
import sys

def test_guide_metadata():
    """Test metadata của guide import"""
    
    # Test data
    test_file_path = "giaoanmau.docx"
    book_id = "test_guide_5"
    
    print("🧪 Test Guide Metadata in Qdrant")
    print("=" * 50)
    
    # Step 1: Upload DOCX guide
    print("1️⃣ Uploading DOCX guide...")
    upload_result = upload_guide(test_file_path, book_id)
    
    if not upload_result:
        print("❌ Upload failed, stopping test")
        return False
    
    task_id = upload_result.get('task_id')
    print(f"✅ Upload successful, task_id: {task_id}")
    
    # Step 2: Wait for processing
    print("\n2️⃣ Waiting for processing...")
    task_result = wait_for_task_completion(task_id)
    
    if not task_result:
        print("❌ Task processing failed")
        return False
    
    # Step 3: Check Supabase upload
    print("\n3️⃣ Checking Supabase upload...")
    file_storage = task_result.get('file_storage', {})
    
    if file_storage.get('uploaded_to_supabase'):
        file_url = file_storage.get('file_url')
        uploaded_at = file_storage.get('uploaded_at')
        print(f"✅ File uploaded to Supabase: {file_url}")
        print(f"⏰ Upload time: {uploaded_at}")
    else:
        print("❌ File NOT uploaded to Supabase")
        return False
    
    # Step 4: Check Qdrant metadata
    print("\n4️⃣ Checking Qdrant metadata...")
    metadata_check = check_qdrant_metadata(book_id, file_url, uploaded_at)
    
    return metadata_check

def upload_guide(file_path, book_id):
    """Upload guide file"""
    url = "http://localhost:8000/api/v1/pdf/import"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (file_path, f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            
            data = {
                'book_id': book_id,
                'create_embeddings': 'true',
                'lesson_id': '1',
                'isImportGuide': 'true'
            }
            
            response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def wait_for_task_completion(task_id, max_wait=120):
    """Wait for task to complete"""
    status_url = f"http://localhost:8000/api/v1/tasks/{task_id}/status"
    result_url = f"http://localhost:8000/api/v1/tasks/{task_id}/result"
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(status_url)
            if response.status_code == 200:
                status = response.json()
                task_status = status.get('status')
                progress = status.get('progress', 0)
                message = status.get('message', '')
                
                print(f"   Status: {task_status} ({progress}%) - {message}")
                
                if task_status == 'completed':
                    # Get result
                    result_response = requests.get(result_url)
                    if result_response.status_code == 200:
                        return result_response.json()
                    else:
                        print(f"❌ Failed to get result: {result_response.status_code}")
                        return None
                        
                elif task_status == 'failed':
                    print(f"❌ Task failed: {status.get('error', 'Unknown error')}")
                    return None
                    
            time.sleep(5)  # Wait 5 seconds before checking again
            
        except Exception as e:
            print(f"❌ Error checking status: {e}")
            time.sleep(5)
    
    print(f"❌ Task timeout after {max_wait} seconds")
    return None

def check_qdrant_metadata(book_id, expected_file_url, expected_uploaded_at):
    """Check metadata in Qdrant"""
    try:
        url = f"http://localhost:8000/api/v1/pdf/textbook/{book_id}/info"
        response = requests.get(url)
        
        if response.status_code == 200:
            info = response.json()
            metadata = info.get('metadata', {})
            
            print(f"✅ Retrieved textbook info from Qdrant")
            
            # Check file_url
            file_url_in_qdrant = metadata.get('file_url')
            if file_url_in_qdrant:
                print(f"✅ file_url found: {file_url_in_qdrant}")
                if file_url_in_qdrant == expected_file_url:
                    print("✅ file_url matches Supabase URL!")
                else:
                    print("⚠️  file_url doesn't match Supabase URL")
                    print(f"   Expected: {expected_file_url}")
                    print(f"   Found: {file_url_in_qdrant}")
            else:
                print("❌ file_url NOT found in metadata")
                return False
            
            # Check uploaded_at
            uploaded_at_in_qdrant = metadata.get('uploaded_at')
            if uploaded_at_in_qdrant:
                print(f"✅ uploaded_at found: {uploaded_at_in_qdrant}")
                if uploaded_at_in_qdrant == expected_uploaded_at:
                    print("✅ uploaded_at matches!")
                else:
                    print("⚠️  uploaded_at doesn't match exactly (might be timezone difference)")
            else:
                print("❌ uploaded_at NOT found in metadata")
                return False
            
            # Show other metadata
            print(f"📋 Other metadata:")
            for key, value in metadata.items():
                if key not in ['file_url', 'uploaded_at']:
                    print(f"   - {key}: {value}")
            
            return True
            
        elif response.status_code == 404:
            print(f"❌ Textbook not found in Qdrant (404)")
            return False
        else:
            print(f"❌ Failed to get Qdrant info: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Qdrant metadata: {e}")
        return False

if __name__ == "__main__":
    success = test_guide_metadata()
    
    if success:
        print("\n🎉 All tests passed! Guide metadata is correctly saved to Qdrant.")
    else:
        print("\n❌ Test failed! Guide metadata is missing in Qdrant.")
        sys.exit(1)
